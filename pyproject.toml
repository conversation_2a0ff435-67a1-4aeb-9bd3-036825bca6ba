[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "caylex-mcp-generator"
version = "1.0.0"
description = "Model Context Protocol generator service using FastMCP 2.0"
authors = [
    {name = "Caylex", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastmcp>=2.0.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "httpx>=0.25.0",
    "aiofiles>=23.2.0",
    "jsonschema>=4.20.0",
    "pyyaml>=6.0.1",
    "openapi-spec-validator>=0.7.1",
    "python-multipart>=0.0.6",
    "zipfile-deflate64>=0.2.0",
    "python-dateutil>=2.8.2",
    "structlog>=23.2.0",
    "jinja2>=3.1.0",
    "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "httpx-mock>=0.10.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
]

[project.scripts]
caylex-mcp-generator = "caylex_mcp_generator.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
