# Caylex MCP Generator API Documentation

## Overview

The Caylex MCP Generator is a FastMCP 2.0 service that generates Model Context Protocol (MCP) servers from OpenAPI specifications. It provides a complete workflow for analyzing, curating, and generating production-ready MCP servers.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, no authentication is required for the API endpoints. In production, you should implement appropriate authentication mechanisms.

## Workflow

The typical workflow involves these steps:

1. **Health Check** - Verify service is running
2. **Create Session** - Start a new workflow session
3. **Upload & Analyze** - Upload OpenAPI spec and get analysis
4. **Curate Tools** - Select and customize tools
5. **Generate Server** - Create the MCP server code
6. **Download** - Get the generated server files

## Endpoints

### Health Check

#### GET /health

Check the health status of the service.

**Response:**
```json
{
  "status": "OK",
  "service": "caylex-mcp-generator",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Session Management

#### POST /sessions

Create a new user session for the workflow.

**Request Body:** Empty

**Response:**
```json
{
  "sessionId": "abc123def456",
  "status": "active"
}
```

#### GET /sessions/{sessionId}

Get session information and status.

**Parameters:**
- `sessionId` (path): Session identifier

**Response:**
```json
{
  "sessionId": "abc123def456",
  "status": "active",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "hasAnalysis": true,
  "hasCuration": false,
  "hasGeneration": false
}
```

**Error Responses:**
- `404`: Session not found
- `400`: Invalid session ID format

### File Upload & Analysis

#### POST /upload

Upload OpenAPI specification file and perform analysis.

**Content-Type:** `multipart/form-data`

**Request:**
- `file` (multipart): OpenAPI JSON/YAML file (max 10MB)
- `sessionId` (form field): Valid session ID

**Response:**
```json
{
  "analysisResult": {
    "openapi": { /* OpenAPI spec object */ },
    "endpoints": [
      {
        "path": "/api/pets",
        "method": "GET",
        "operationId": "listPets",
        "summary": "List all pets",
        "description": "Returns a list of pets",
        "parameters": [ /* parameter objects */ ],
        "relevanceScore": 85,
        "functionalityType": "core",
        "workflowTags": ["data_retrieval"],
        "complexityLevel": "simple",
        "recommendedInTemplates": ["basic_crud"],
        "originalTool": { /* tool definition */ }
      }
    ],
    "servers": ["https://api.example.com"],
    "toolCount": 15,
    "complexity": "moderate",
    "filteredTools": [
      {
        "operationId": "problematicEndpoint",
        "method": "POST",
        "path": "/broken",
        "reason": "Schema validation failed",
        "unresolvedRefs": ["#/components/schemas/Missing"]
      }
    ],
    "enhancedAnalysis": {
      "workflowGroups": { /* AI-enhanced groupings */ },
      "selectionTemplates": { /* template recommendations */ },
      "recommendations": { /* AI recommendations */ },
      "highRelevanceTools": [ /* tools with score >= 80 */ ],
      "relevanceDistribution": {
        "high": 5,
        "medium": 8,
        "low": 2
      }
    }
  }
}
```

**Error Responses:**
- `400`: No file uploaded, invalid session ID, or invalid file format

### Tool Curation

#### POST /curate

Apply tool selection and customization preferences.

**Request:**
```json
{
  "sessionId": "abc123def456",
  "selectedTools": ["tool_0", "tool_3", "tool_7"],
  "templateType": "basic_crud",
  "customizations": {
    "serverName": "my-api-server",
    "includeAuth": true,
    "riskLevel": "medium",
    "maxTools": 10,
    "excludePatterns": ["admin", "internal"],
    "includePatterns": ["user", "data"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tool curation completed successfully",
  "curatedToolCount": 8,
  "appliedFilters": ["user_selection (3 tools)", "template_basic_crud", "risk_medium"]
}
```

**Template Types:**
- `basic_crud`: Essential CRUD operations
- `full_api`: All available endpoints
- `read_only`: Read-only operations
- `admin_tools`: Administrative functions
- `core_features`: Core functionality only
- `custom`: No template filtering

**Risk Levels:**
- `low`: Only safe read operations
- `medium`: Read and basic write operations
- `high`: All operations including destructive ones

### MCP Server Generation

#### POST /generate

Generate MCP server code based on curated tools.

**Request:**
```json
{
  "sessionId": "abc123def456",
  "config": {
    "serverName": "generated-mcp-server",
    "serverVersion": "1.0.0",
    "transport": "streamable-http",
    "port": 3000,
    "name": "My API Server",
    "description": "Generated from OpenAPI spec",
    "includeAuth": true,
    "includeLogging": true,
    "includeCors": true,
    "typescript": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "progress": 100,
  "files": [
    {
      "name": "index.ts",
      "type": "typescript",
      "path": "src/index.ts",
      "size": 15420
    },
    {
      "name": "package.json",
      "type": "json",
      "path": "package.json",
      "size": 1250
    }
  ],
  "downloadUrl": "/download/abc123def456",
  "metadata": {
    "generator": "caylex-mcp-generator",
    "toolCount": 12,
    "transport": "streamable-http",
    "nextSteps": [
      "Install dependencies with npm install",
      "Configure environment variables",
      "Run the server with npm start"
    ],
    "dependencies": ["fastmcp>=2.0.0", "axios>=1.6.0"],
    "environmentVariables": {
      "API_KEY": "Your API key for authentication"
    }
  }
}
```

**Transport Types:**
- `stdio`: Standard input/output (default for MCP)
- `streamable-http`: HTTP server with streaming support
- `sse`: Server-sent events (legacy)

### File Downloads

#### GET /download/{sessionId}

Download generated MCP server as ZIP file.

**Parameters:**
- `sessionId` (path): Session identifier

**Response:** Binary ZIP file with `Content-Disposition: attachment`

#### POST /download/{sessionId}/enhanced

Download enhanced server with additional deployment files.

**Request:**
```json
{
  "deploymentFiles": [
    {
      "name": "Dockerfile",
      "content": "FROM node:18-alpine\n...",
      "description": "Docker container configuration"
    }
  ]
}
```

**Response:** Binary ZIP file with additional deployment files

### Directory Integration

#### POST /integrate/add-to-directory

Register generated server with Caylex Directory.

**Request:**
```json
{
  "sessionId": "abc123def456",
  "serverConfig": {
    "serverName": "my-api-server",
    "deploymentUrl": "https://my-server.com",
    "description": "API server for managing resources",
    "generateDeploymentFiles": true,
    "tags": ["api", "crud", "petstore"],
    "category": "data-management"
  }
}
```

**Response:**
```json
{
  "success": true,
  "serverId": "srv_1234567890",
  "deploymentFiles": [
    {
      "name": "k8s-deployment.yaml",
      "content": "apiVersion: apps/v1\n...",
      "description": "Kubernetes deployment configuration"
    }
  ],
  "message": "Server registered successfully",
  "directoryUrl": "https://directory.caylex.com/servers/srv_1234567890"
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "error": "error_type",
  "message": "Human-readable error message",
  "details": {
    "additional": "error details"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Common error types:
- `validation_error`: Input validation failed
- `session_not_found`: Invalid or expired session
- `file_not_found`: Requested file not available
- `internal_server_error`: Unexpected server error

## Rate Limits

Currently no rate limits are enforced, but consider implementing them in production:
- 100 requests per minute per IP
- 10 file uploads per hour per session
- 5 server generations per hour per session

## Examples

See the `examples/example_usage.py` file for a complete workflow example.
