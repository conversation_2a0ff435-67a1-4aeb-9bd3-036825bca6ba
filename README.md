# Caylex MCP Generator

A FastMCP 2.0 service for generating Model Context Protocol (MCP) servers from OpenAPI specifications.

## Features

- **OpenAPI Analysis**: Upload and analyze OpenAPI specifications with AI-enhanced insights
- **Tool Curation**: Select and customize tools based on relevance and functionality
- **MCP Server Generation**: Generate FastMCP 2.0 compatible servers with TypeScript/JavaScript
- **Session Management**: Stateful workflow management for the generation process
- **File Downloads**: Download generated servers as ZIP files with deployment configurations
- **Directory Integration**: Register generated servers with Caylex Directory

## API Endpoints

### Health Check
- `GET /health` - Service health status

### Session Management
- `POST /sessions` - Create new session
- `GET /sessions/{sessionId}` - Get session status

### OpenAPI Analysis
- `POST /upload` - Upload and analyze OpenAPI specification

### Tool Curation
- `POST /curate` - Apply tool selection and customization

### Server Generation
- `POST /generate` - Generate MCP server code
- `GET /download/{sessionId}` - Download generated server
- `POST /download/{sessionId}/enhanced` - Download with deployment files

### Directory Integration
- `POST /integrate/add-to-directory` - Register with Caylex Directory

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e ".[dev]"
```

## Usage

### Running the Service

```bash
# Using uvicorn directly
uvicorn caylex_mcp_generator.main:app --host 0.0.0.0 --port 8000

# Using the CLI script
caylex-mcp-generator

# With environment variables
CAYLEX_MCP_HOST=0.0.0.0 CAYLEX_MCP_PORT=8000 caylex-mcp-generator
```

### Environment Variables

All configuration can be set via environment variables with the `CAYLEX_MCP_` prefix:

- `CAYLEX_MCP_HOST` - Server host (default: 127.0.0.1)
- `CAYLEX_MCP_PORT` - Server port (default: 8000)
- `CAYLEX_MCP_DEBUG` - Debug mode (default: false)
- `CAYLEX_MCP_MAX_FILE_SIZE` - Max upload size in bytes (default: 10MB)
- `CAYLEX_MCP_SESSION_TIMEOUT` - Session timeout in seconds (default: 3600)

## Development

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black src tests
isort src tests

# Type checking
mypy src
```

## License

MIT License - see LICENSE file for details.
