"""
Example usage of the Caylex MCP Generator API.
"""

import asyncio
import json
from pathlib import Path

import httpx


async def example_workflow():
    """Demonstrate the complete MCP generator workflow."""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        print("🚀 Starting MCP Generator Example Workflow")
        print()
        
        # 1. Check health
        print("1. Checking service health...")
        health_response = await client.get(f"{base_url}/health")
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"   ✅ Service is healthy: {health_data['status']}")
        else:
            print(f"   ❌ Service health check failed: {health_response.status_code}")
            return
        print()
        
        # 2. Create session
        print("2. Creating session...")
        session_response = await client.post(f"{base_url}/sessions")
        if session_response.status_code == 200:
            session_data = session_response.json()
            session_id = session_data["session_id"]
            print(f"   ✅ Session created: {session_id}")
        else:
            print(f"   ❌ Session creation failed: {session_response.status_code}")
            return
        print()
        
        # 3. Upload OpenAPI specification
        print("3. Uploading OpenAPI specification...")
        
        # Sample OpenAPI spec
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": "Pet Store API",
                "version": "1.0.0",
                "description": "A simple pet store API"
            },
            "servers": [
                {"url": "https://petstore.swagger.io/v2"}
            ],
            "paths": {
                "/pets": {
                    "get": {
                        "operationId": "listPets",
                        "summary": "List all pets",
                        "description": "Returns a list of pets",
                        "parameters": [
                            {
                                "name": "limit",
                                "in": "query",
                                "description": "How many items to return at one time (max 100)",
                                "required": False,
                                "schema": {"type": "integer", "maximum": 100}
                            }
                        ],
                        "responses": {
                            "200": {
                                "description": "A paged array of pets",
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "array",
                                            "items": {"$ref": "#/components/schemas/Pet"}
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "post": {
                        "operationId": "createPet",
                        "summary": "Create a pet",
                        "description": "Creates a new pet",
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/Pet"}
                                }
                            }
                        },
                        "responses": {
                            "201": {
                                "description": "Pet created",
                                "content": {
                                    "application/json": {
                                        "schema": {"$ref": "#/components/schemas/Pet"}
                                    }
                                }
                            }
                        }
                    }
                },
                "/pets/{petId}": {
                    "get": {
                        "operationId": "getPet",
                        "summary": "Info for a specific pet",
                        "description": "Returns a specific pet",
                        "parameters": [
                            {
                                "name": "petId",
                                "in": "path",
                                "required": True,
                                "description": "The id of the pet to retrieve",
                                "schema": {"type": "string"}
                            }
                        ],
                        "responses": {
                            "200": {
                                "description": "Expected response to a valid request",
                                "content": {
                                    "application/json": {
                                        "schema": {"$ref": "#/components/schemas/Pet"}
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "components": {
                "schemas": {
                    "Pet": {
                        "type": "object",
                        "required": ["id", "name"],
                        "properties": {
                            "id": {"type": "integer", "format": "int64"},
                            "name": {"type": "string"},
                            "tag": {"type": "string"}
                        }
                    }
                }
            }
        }
        
        # Upload the spec
        files = {
            "file": ("petstore.json", json.dumps(openapi_spec), "application/json")
        }
        data = {"session_id": session_id}
        
        upload_response = await client.post(f"{base_url}/upload", files=files, data=data)
        if upload_response.status_code == 200:
            analysis_data = upload_response.json()
            analysis_result = analysis_data["analysis_result"]
            print(f"   ✅ Analysis completed:")
            print(f"      - Tools found: {analysis_result['tool_count']}")
            print(f"      - Complexity: {analysis_result['complexity']}")
            print(f"      - High relevance tools: {len(analysis_result['enhanced_analysis']['high_relevance_tools'])}")
        else:
            print(f"   ❌ Upload failed: {upload_response.status_code}")
            print(f"      Error: {upload_response.text}")
            return
        print()
        
        # 4. Curate tools
        print("4. Curating tools...")
        curation_request = {
            "session_id": session_id,
            "selected_tools": ["listPets", "createPet", "getPet"],
            "template_type": "basic_crud",
            "customizations": {
                "server_name": "petstore-mcp-server",
                "include_auth": True,
                "risk_level": "medium",
                "max_tools": 10
            }
        }
        
        curation_response = await client.post(f"{base_url}/curate", json=curation_request)
        if curation_response.status_code == 200:
            curation_data = curation_response.json()
            print(f"   ✅ Curation completed:")
            print(f"      - Final tool count: {curation_data['curated_tool_count']}")
            print(f"      - Applied filters: {', '.join(curation_data['applied_filters'])}")
        else:
            print(f"   ❌ Curation failed: {curation_response.status_code}")
            return
        print()
        
        # 5. Generate MCP server
        print("5. Generating MCP server...")
        generation_request = {
            "session_id": session_id,
            "config": {
                "server_name": "petstore-mcp-server",
                "server_version": "1.0.0",
                "transport": "streamable-http",
                "port": 3000,
                "name": "Pet Store MCP Server",
                "description": "MCP server for Pet Store API",
                "include_auth": True,
                "include_logging": True,
                "include_cors": True,
                "typescript": True
            }
        }
        
        generation_response = await client.post(f"{base_url}/generate", json=generation_request)
        if generation_response.status_code == 200:
            generation_data = generation_response.json()
            print(f"   ✅ Generation completed:")
            print(f"      - Files generated: {len(generation_data['files'])}")
            print(f"      - Download URL: {generation_data['download_url']}")
            print(f"      - Tools in server: {generation_data['metadata']['tool_count']}")
        else:
            print(f"   ❌ Generation failed: {generation_response.status_code}")
            return
        print()
        
        # 6. Download the generated server
        print("6. Downloading generated server...")
        download_response = await client.get(f"{base_url}/download/{session_id}")
        if download_response.status_code == 200:
            # Save the ZIP file
            output_path = Path("petstore-mcp-server.zip")
            with open(output_path, "wb") as f:
                f.write(download_response.content)
            print(f"   ✅ Server downloaded: {output_path}")
            print(f"      File size: {len(download_response.content)} bytes")
        else:
            print(f"   ❌ Download failed: {download_response.status_code}")
            return
        print()
        
        print("🎉 Workflow completed successfully!")
        print()
        print("Next steps:")
        print("1. Extract the ZIP file")
        print("2. Run 'npm install' to install dependencies")
        print("3. Configure your API key in .env file")
        print("4. Run 'npm start' to start the MCP server")


if __name__ == "__main__":
    asyncio.run(example_workflow())
