"""
Integration tests for API endpoints.
"""

import json
from io import BytesIO

import pytest
from fastapi.testclient import TestClient


class TestHealthEndpoint:
    """Test the health check endpoint."""
    
    def test_health_check(self, client: TestClient):
        """Test health check returns correct response."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "OK"
        assert data["service"] == "caylex-mcp-generator"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data


class TestSessionEndpoints:
    """Test session management endpoints."""
    
    def test_create_session(self, client: TestClient):
        """Test session creation."""
        response = client.post("/sessions")
        assert response.status_code == 200
        
        data = response.json()
        assert "session_id" in data
        assert data["status"] == "active"
    
    def test_get_session(self, client: TestClient):
        """Test getting session information."""
        # Create a session first
        create_response = client.post("/sessions")
        session_id = create_response.json()["session_id"]
        
        # Get session info
        response = client.get(f"/sessions/{session_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["session_id"] == session_id
        assert data["status"] == "active"
        assert data["has_analysis"] is False
        assert data["has_curation"] is False
        assert data["has_generation"] is False
    
    def test_get_nonexistent_session(self, client: TestClient):
        """Test getting non-existent session returns 404."""
        response = client.get("/sessions/nonexistent-id")
        assert response.status_code == 400  # Invalid format
    
    def test_get_session_invalid_format(self, client: TestClient):
        """Test getting session with invalid ID format."""
        response = client.get("/sessions/invalid-uuid")
        assert response.status_code == 400


class TestUploadEndpoint:
    """Test file upload and analysis endpoint."""
    
    def test_upload_valid_json(self, client: TestClient, sample_openapi_json):
        """Test uploading valid OpenAPI JSON."""
        # Create session first
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        # Prepare file upload
        json_content = json.dumps(sample_openapi_json)
        files = {"file": ("test_api.json", BytesIO(json_content.encode()), "application/json")}
        data = {"session_id": session_id}
        
        response = client.post("/upload", files=files, data=data)
        assert response.status_code == 200
        
        result = response.json()
        assert "analysis_result" in result
        analysis = result["analysis_result"]
        assert analysis["tool_count"] > 0
        assert len(analysis["endpoints"]) > 0
        assert analysis["complexity"] in ["simple", "moderate", "complex"]
    
    def test_upload_invalid_session(self, client: TestClient, sample_openapi_json):
        """Test upload with invalid session ID."""
        json_content = json.dumps(sample_openapi_json)
        files = {"file": ("test_api.json", BytesIO(json_content.encode()), "application/json")}
        data = {"session_id": "invalid-session-id"}
        
        response = client.post("/upload", files=files, data=data)
        assert response.status_code == 400
    
    def test_upload_no_file(self, client: TestClient):
        """Test upload without file."""
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        data = {"session_id": session_id}
        response = client.post("/upload", data=data)
        assert response.status_code == 422  # Validation error
    
    def test_upload_invalid_file_format(self, client: TestClient):
        """Test upload with invalid file format."""
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        files = {"file": ("test.txt", BytesIO(b"invalid content"), "text/plain")}
        data = {"session_id": session_id}
        
        response = client.post("/upload", files=files, data=data)
        assert response.status_code == 400


class TestCurationEndpoint:
    """Test tool curation endpoint."""
    
    def test_curate_tools(self, client: TestClient, sample_openapi_json, sample_curation_request):
        """Test tool curation."""
        # Create session and upload file
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        json_content = json.dumps(sample_openapi_json)
        files = {"file": ("test_api.json", BytesIO(json_content.encode()), "application/json")}
        data = {"session_id": session_id}
        client.post("/upload", files=files, data=data)
        
        # Curate tools
        curation_data = {**sample_curation_request, "session_id": session_id}
        response = client.post("/curate", json=curation_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "curated_tool_count" in result
        assert "applied_filters" in result
    
    def test_curate_without_analysis(self, client: TestClient, sample_curation_request):
        """Test curation without prior analysis."""
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        curation_data = {**sample_curation_request, "session_id": session_id}
        response = client.post("/curate", json=curation_data)
        assert response.status_code == 400


class TestGenerationEndpoint:
    """Test MCP server generation endpoint."""
    
    def test_generate_server(
        self, 
        client: TestClient, 
        sample_openapi_json, 
        sample_curation_request,
        sample_generation_config
    ):
        """Test MCP server generation."""
        # Create session, upload, and curate
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        # Upload
        json_content = json.dumps(sample_openapi_json)
        files = {"file": ("test_api.json", BytesIO(json_content.encode()), "application/json")}
        data = {"session_id": session_id}
        client.post("/upload", files=files, data=data)
        
        # Curate
        curation_data = {**sample_curation_request, "session_id": session_id}
        client.post("/curate", json=curation_data)
        
        # Generate
        generation_data = {
            "session_id": session_id,
            "config": sample_generation_config
        }
        response = client.post("/generate", json=generation_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert result["progress"] == 100
        assert len(result["files"]) > 0
        assert "download_url" in result
        assert result["metadata"]["tool_count"] > 0
    
    def test_generate_without_curation(self, client: TestClient, sample_generation_config):
        """Test generation without prior curation."""
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        generation_data = {
            "session_id": session_id,
            "config": sample_generation_config
        }
        response = client.post("/generate", json=generation_data)
        assert response.status_code == 400


class TestDownloadEndpoints:
    """Test file download endpoints."""
    
    def test_download_nonexistent_session(self, client: TestClient):
        """Test download for non-existent session."""
        response = client.get("/download/nonexistent-session")
        assert response.status_code == 404
    
    def test_download_without_generation(self, client: TestClient):
        """Test download without prior generation."""
        session_response = client.post("/sessions")
        session_id = session_response.json()["session_id"]
        
        response = client.get(f"/download/{session_id}")
        assert response.status_code == 404
