"""
Unit tests for service classes.
"""

import json
from unittest.mock import AsyncMock, MagicMock

import pytest

from src.caylex_mcp_generator.models.analysis import AnalysisResult
from src.caylex_mcp_generator.models.curation import CurationRequest, TemplateType, ToolCustomization, RiskLevel
from src.caylex_mcp_generator.models.generation import GenerationConfig, TransportType
from src.caylex_mcp_generator.services.openapi_analyzer import OpenAPIAnalyzer
from src.caylex_mcp_generator.services.tool_curator import ToolCurator
from src.caylex_mcp_generator.services.code_generator import CodeGenerator
from src.caylex_mcp_generator.services.session_manager import SessionManager


class TestOpenAPIAnalyzer:
    """Test OpenAPI analysis service."""
    
    @pytest.fixture
    def analyzer(self):
        return OpenAPIAnalyzer()
    
    @pytest.mark.asyncio
    async def test_analyze_simple_spec(self, analyzer, sample_openapi_json):
        """Test analysis of a simple OpenAPI specification."""
        content = json.dumps(sample_openapi_json).encode('utf-8')
        
        result = await analyzer.analyze_openapi_spec(content, "test.json")
        
        assert isinstance(result, AnalysisResult)
        assert result.tool_count > 0
        assert len(result.endpoints) > 0
        assert result.complexity in ["simple", "moderate", "complex"]
        assert len(result.servers) > 0
    
    @pytest.mark.asyncio
    async def test_analyze_invalid_spec(self, analyzer):
        """Test analysis of invalid OpenAPI specification."""
        invalid_content = b'{"invalid": "json"}'
        
        with pytest.raises(ValueError):
            await analyzer.analyze_openapi_spec(invalid_content, "invalid.json")
    
    def test_calculate_relevance_score(self, analyzer):
        """Test relevance score calculation."""
        operation = {
            "summary": "Get user data",
            "description": "Retrieve user information",
            "operationId": "getUser",
            "responses": {"200": {"description": "Success"}}
        }
        
        score = analyzer._calculate_relevance_score("/users/{id}", "GET", operation, [])
        assert 0 <= score <= 100
    
    def test_determine_functionality_type(self, analyzer):
        """Test functionality type determination."""
        # Test core functionality
        func_type = analyzer._determine_functionality_type("/users", "POST", {})
        assert func_type == "core"
        
        # Test admin functionality
        func_type = analyzer._determine_functionality_type("/admin/users", "GET", {})
        assert func_type == "admin"
        
        # Test monitoring functionality
        func_type = analyzer._determine_functionality_type("/health", "GET", {})
        assert func_type == "monitoring"


class TestToolCurator:
    """Test tool curation service."""
    
    @pytest.fixture
    def curator(self):
        return ToolCurator()
    
    @pytest.fixture
    def sample_analysis_result(self, sample_openapi_json):
        """Create a sample analysis result for testing."""
        # This would normally come from the analyzer
        from src.caylex_mcp_generator.models.analysis import OpenAPIEndpoint, AnalysisResult, EnhancedAnalysis, RelevanceDistribution, AIRecommendation
        
        endpoints = [
            OpenAPIEndpoint(
                path="/users",
                method="GET",
                operation_id="listUsers",
                summary="List users",
                description="Get all users",
                parameters=[],
                relevance_score=85,
                functionality_type="core",
                workflow_tags=["data_retrieval"],
                complexity_level="simple",
                recommended_in_templates=["basic_crud"],
                original_tool={}
            ),
            OpenAPIEndpoint(
                path="/users",
                method="POST",
                operation_id="createUser",
                summary="Create user",
                description="Create a new user",
                parameters=[],
                relevance_score=90,
                functionality_type="core",
                workflow_tags=["data_creation"],
                complexity_level="moderate",
                recommended_in_templates=["basic_crud"],
                original_tool={}
            )
        ]
        
        enhanced_analysis = EnhancedAnalysis(
            workflow_groups={},
            selection_templates={},
            recommendations=AIRecommendation(
                summary="Test API",
                strengths=["Well documented"],
                potential_issues=[],
                suggested_improvements=[],
                best_practices=[]
            ),
            high_relevance_tools=["listUsers", "createUser"],
            relevance_distribution=RelevanceDistribution(high=2, medium=0, low=0)
        )
        
        return AnalysisResult(
            openapi=sample_openapi_json,
            endpoints=endpoints,
            servers=["https://api.test.com"],
            tool_count=2,
            complexity="simple",
            filtered_tools=[],
            enhanced_analysis=enhanced_analysis
        )
    
    @pytest.mark.asyncio
    async def test_curate_basic_crud(self, curator, sample_analysis_result):
        """Test basic CRUD curation."""
        request = CurationRequest(
            session_id="test-session",
            selected_tools=["listUsers", "createUser"],
            template_type=TemplateType.BASIC_CRUD,
            customizations=ToolCustomization(
                server_name="test-server",
                include_auth=True,
                risk_level=RiskLevel.MEDIUM
            )
        )
        
        result = await curator.curate_tools(sample_analysis_result, request)
        
        assert "curated_endpoints" in result
        assert result["final_count"] <= result["original_count"]
        assert "applied_filters" in result
    
    @pytest.mark.asyncio
    async def test_curate_read_only(self, curator, sample_analysis_result):
        """Test read-only curation."""
        request = CurationRequest(
            session_id="test-session",
            selected_tools=[],
            template_type=TemplateType.READ_ONLY,
            customizations=ToolCustomization(
                server_name="test-server",
                risk_level=RiskLevel.LOW
            )
        )
        
        result = await curator.curate_tools(sample_analysis_result, request)
        
        # Should only include GET endpoints
        curated_endpoints = result["curated_endpoints"]
        for endpoint in curated_endpoints:
            assert endpoint["method"] == "GET"


class TestCodeGenerator:
    """Test code generation service."""
    
    @pytest.fixture
    def generator(self):
        return CodeGenerator()
    
    @pytest.fixture
    def sample_endpoints(self):
        """Sample endpoints for generation testing."""
        from src.caylex_mcp_generator.models.analysis import OpenAPIEndpoint
        
        return [
            OpenAPIEndpoint(
                path="/users",
                method="GET",
                operation_id="listUsers",
                summary="List users",
                description="Get all users",
                parameters=[],
                relevance_score=85,
                functionality_type="core",
                workflow_tags=["data_retrieval"],
                complexity_level="simple",
                recommended_in_templates=["basic_crud"],
                original_tool={}
            )
        ]
    
    @pytest.mark.asyncio
    async def test_generate_typescript_server(self, generator, sample_endpoints):
        """Test TypeScript server generation."""
        config = GenerationConfig(
            server_name="test-server",
            server_version="1.0.0",
            transport=TransportType.STREAMABLE_HTTP,
            port=3000,
            name="Test Server",
            description="A test server",
            typescript=True
        )
        
        result = await generator.generate_mcp_server(
            endpoints=sample_endpoints,
            servers=["https://api.test.com"],
            config=config
        )
        
        assert "generated_files" in result
        assert "metadata" in result
        assert "zip_path" in result
        
        files = result["generated_files"]
        file_names = [f.name for f in files]
        assert "index.ts" in file_names
        assert "package.json" in file_names
        assert "README.md" in file_names
        assert "tsconfig.json" in file_names
    
    @pytest.mark.asyncio
    async def test_generate_javascript_server(self, generator, sample_endpoints):
        """Test JavaScript server generation."""
        config = GenerationConfig(
            server_name="test-server",
            server_version="1.0.0",
            transport=TransportType.STDIO,
            name="Test Server",
            description="A test server",
            typescript=False
        )
        
        result = await generator.generate_mcp_server(
            endpoints=sample_endpoints,
            servers=["https://api.test.com"],
            config=config
        )
        
        files = result["generated_files"]
        file_names = [f.name for f in files]
        assert "index.js" in file_names
        assert "tsconfig.json" not in file_names


class TestSessionManager:
    """Test session management service."""
    
    @pytest.fixture
    def session_manager(self):
        return SessionManager()
    
    @pytest.mark.asyncio
    async def test_create_session(self, session_manager):
        """Test session creation."""
        session = await session_manager.create_session()
        
        assert session.session_id is not None
        assert session.status.value == "active"
        assert session.has_analysis is False
        assert session.has_curation is False
        assert session.has_generation is False
    
    @pytest.mark.asyncio
    async def test_get_session(self, session_manager):
        """Test session retrieval."""
        # Create a session
        created_session = await session_manager.create_session()
        session_id = str(created_session.session_id)
        
        # Retrieve it
        retrieved_session = await session_manager.get_session(session_id)
        
        assert retrieved_session is not None
        assert str(retrieved_session.session_id) == session_id
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_session(self, session_manager):
        """Test retrieving non-existent session."""
        result = await session_manager.get_session("nonexistent-id")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_delete_session(self, session_manager):
        """Test session deletion."""
        # Create a session
        session = await session_manager.create_session()
        session_id = str(session.session_id)
        
        # Delete it
        deleted = await session_manager.delete_session(session_id)
        assert deleted is True
        
        # Verify it's gone
        retrieved = await session_manager.get_session(session_id)
        assert retrieved is None
