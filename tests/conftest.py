"""
Pytest configuration and fixtures.
"""

import asyncio
import tempfile
from pathlib import Path
from typing import Async<PERSON>enerator, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient

from src.caylex_mcp_generator.main import app
from src.caylex_mcp_generator.services.session_manager import session_manager


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client for the FastAPI application."""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac


@pytest_asyncio.fixture
async def test_session():
    """Create a test session."""
    session = await session_manager.create_session()
    yield session
    # Cleanup
    await session_manager.delete_session(str(session.session_id))


@pytest.fixture
def sample_openapi_json():
    """Sample OpenAPI JSON specification."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0",
            "description": "A test API for MCP generator"
        },
        "servers": [
            {"url": "https://api.test.com"}
        ],
        "paths": {
            "/users": {
                "get": {
                    "operationId": "listUsers",
                    "summary": "List all users",
                    "description": "Retrieve a list of all users",
                    "responses": {
                        "200": {
                            "description": "List of users",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {"$ref": "#/components/schemas/User"}
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "operationId": "createUser",
                    "summary": "Create a new user",
                    "description": "Create a new user in the system",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/User"}
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "User created",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/User"}
                                }
                            }
                        }
                    }
                }
            },
            "/users/{userId}": {
                "get": {
                    "operationId": "getUser",
                    "summary": "Get user by ID",
                    "description": "Retrieve a specific user by ID",
                    "parameters": [
                        {
                            "name": "userId",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "User details",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/User"}
                                }
                            }
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "User": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "name": {"type": "string"},
                        "email": {"type": "string", "format": "email"}
                    },
                    "required": ["id", "name", "email"]
                }
            }
        }
    }


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_curation_request():
    """Sample curation request data."""
    return {
        "selected_tools": ["listUsers", "createUser"],
        "template_type": "basic_crud",
        "customizations": {
            "server_name": "test-api-server",
            "include_auth": True,
            "risk_level": "medium",
            "max_tools": 10
        }
    }


@pytest.fixture
def sample_generation_config():
    """Sample generation configuration."""
    return {
        "server_name": "test-mcp-server",
        "server_version": "1.0.0",
        "transport": "streamable-http",
        "port": 3000,
        "name": "Test MCP Server",
        "description": "A test MCP server generated from OpenAPI",
        "include_auth": True,
        "include_logging": True,
        "include_cors": True,
        "typescript": True
    }
