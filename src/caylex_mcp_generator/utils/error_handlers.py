"""
Error handling utilities and middleware.
"""

import traceback
from datetime import datetime, timezone
from typing import Any, Dict

import structlog
from fastapi import FastAP<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError

from ..models.api import ErrorResponse
from .validation import ValidationError

logger = structlog.get_logger(__name__)


def setup_error_handlers(app: FastAPI) -> None:
    """Setup global error handlers for the FastAPI application."""
    
    @app.exception_handler(ValidationError)
    async def validation_error_handler(request: Request, exc: ValidationError) -> JSONResponse:
        """Handle custom validation errors."""
        logger.warning(
            "Validation error",
            path=request.url.path,
            method=request.method,
            error=str(exc)
        )
        
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ErrorResponse(
                error="validation_error",
                message=str(exc),
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(RequestValidationError)
    async def request_validation_error_handler(
        request: Request, 
        exc: RequestValidationError
    ) -> JSONResponse:
        """Handle FastAPI request validation errors."""
        logger.warning(
            "Request validation error",
            path=request.url.path,
            method=request.method,
            errors=exc.errors()
        )
        
        # Format validation errors for better user experience
        error_details = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"])
            error_details.append(f"{field}: {error['msg']}")
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=ErrorResponse(
                error="request_validation_error",
                message="Request validation failed",
                details={"validation_errors": error_details},
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(PydanticValidationError)
    async def pydantic_validation_error_handler(
        request: Request, 
        exc: PydanticValidationError
    ) -> JSONResponse:
        """Handle Pydantic validation errors."""
        logger.warning(
            "Pydantic validation error",
            path=request.url.path,
            method=request.method,
            error=str(exc)
        )
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=ErrorResponse(
                error="data_validation_error",
                message="Data validation failed",
                details={"validation_error": str(exc)},
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(FileNotFoundError)
    async def file_not_found_error_handler(
        request: Request, 
        exc: FileNotFoundError
    ) -> JSONResponse:
        """Handle file not found errors."""
        logger.error(
            "File not found error",
            path=request.url.path,
            method=request.method,
            error=str(exc)
        )
        
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ErrorResponse(
                error="file_not_found",
                message="Requested file not found",
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(PermissionError)
    async def permission_error_handler(
        request: Request, 
        exc: PermissionError
    ) -> JSONResponse:
        """Handle permission errors."""
        logger.error(
            "Permission error",
            path=request.url.path,
            method=request.method,
            error=str(exc)
        )
        
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ErrorResponse(
                error="permission_denied",
                message="Permission denied",
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(TimeoutError)
    async def timeout_error_handler(
        request: Request, 
        exc: TimeoutError
    ) -> JSONResponse:
        """Handle timeout errors."""
        logger.error(
            "Timeout error",
            path=request.url.path,
            method=request.method,
            error=str(exc)
        )
        
        return JSONResponse(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            content=ErrorResponse(
                error="request_timeout",
                message="Request timed out",
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError) -> JSONResponse:
        """Handle value errors."""
        logger.warning(
            "Value error",
            path=request.url.path,
            method=request.method,
            error=str(exc)
        )
        
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ErrorResponse(
                error="invalid_value",
                message=str(exc),
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """Handle all other exceptions."""
        # Log the full traceback for debugging
        logger.error(
            "Unhandled exception",
            path=request.url.path,
            method=request.method,
            error=str(exc),
            traceback=traceback.format_exc()
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ErrorResponse(
                error="internal_server_error",
                message="An internal server error occurred",
                timestamp=datetime.now(timezone.utc).isoformat()
            ).model_dump()
        )


def create_error_response(
    error_type: str,
    message: str,
    status_code: int = 400,
    details: Dict[str, Any] = None
) -> JSONResponse:
    """Create a standardized error response."""
    return JSONResponse(
        status_code=status_code,
        content=ErrorResponse(
            error=error_type,
            message=message,
            details=details,
            timestamp=datetime.now(timezone.utc).isoformat()
        ).model_dump()
    )


def log_and_raise_error(
    logger_instance: Any,
    error_message: str,
    exception_class: type = Exception,
    **log_kwargs
) -> None:
    """Log an error and raise an exception."""
    logger_instance.error(error_message, **log_kwargs)
    raise exception_class(error_message)
