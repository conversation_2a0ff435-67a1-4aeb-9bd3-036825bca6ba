"""
Input validation utilities.
"""

import re
from typing import Optional
from uuid import U<PERSON><PERSON>

import structlog
from fastapi import UploadFile

from ..config import settings

logger = structlog.get_logger(__name__)


class ValidationError(Exception):
    """Custom validation error."""
    pass


def validate_session_id(session_id: str) -> bool:
    """Validate session ID format."""
    try:
        # Check if it's a valid UUID
        UUID(session_id)
        return True
    except ValueError:
        return False


def validate_file_upload(file: UploadFile) -> None:
    """Validate uploaded file."""
    if not file.filename:
        raise ValidationError("No file uploaded")
    
    # Check file extension
    allowed_extensions = {'.json', '.yaml', '.yml'}
    file_ext = '.' + file.filename.split('.')[-1].lower() if '.' in file.filename else ''
    if file_ext not in allowed_extensions:
        raise ValidationError(
            f"Invalid file format '{file_ext}'. Only JSON and YAML files are supported"
        )
    
    # File size will be checked after reading content
    logger.debug("File upload validation passed", filename=file.filename, extension=file_ext)


def validate_server_name(server_name: str) -> None:
    """Validate server name format."""
    if not server_name:
        raise ValidationError("Server name is required")
    
    if len(server_name) < 3:
        raise ValidationError("Server name must be at least 3 characters long")
    
    if len(server_name) > 50:
        raise ValidationError("Server name must be less than 50 characters")
    
    # Check for valid characters (alphanumeric, hyphens, underscores)
    if not re.match(r'^[a-zA-Z0-9_-]+$', server_name):
        raise ValidationError(
            "Server name can only contain letters, numbers, hyphens, and underscores"
        )
    
    # Cannot start or end with hyphen/underscore
    if server_name.startswith(('-', '_')) or server_name.endswith(('-', '_')):
        raise ValidationError("Server name cannot start or end with hyphen or underscore")


def validate_port(port: int) -> None:
    """Validate port number."""
    if port < 1 or port > 65535:
        raise ValidationError("Port must be between 1 and 65535")
    
    # Check for commonly reserved ports
    reserved_ports = {22, 25, 53, 80, 110, 143, 443, 993, 995}
    if port in reserved_ports:
        logger.warning("Using reserved port", port=port)


def validate_tool_selection(selected_tools: list, available_tools: list) -> None:
    """Validate tool selection."""
    if not selected_tools:
        return  # Empty selection is allowed
    
    # Check if all selected tools are available
    available_tool_ids = {tool.operation_id for tool in available_tools}
    invalid_tools = [tool_id for tool_id in selected_tools if tool_id not in available_tool_ids]
    
    if invalid_tools:
        raise ValidationError(f"Invalid tool IDs: {', '.join(invalid_tools)}")
    
    # Check for reasonable limits
    if len(selected_tools) > settings.max_endpoints:
        raise ValidationError(f"Too many tools selected. Maximum is {settings.max_endpoints}")


def validate_deployment_url(url: str) -> None:
    """Validate deployment URL format."""
    if not url:
        raise ValidationError("Deployment URL is required")
    
    # Basic URL validation
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    if not url_pattern.match(url):
        raise ValidationError("Invalid deployment URL format")


def validate_environment_variables(env_vars: dict) -> None:
    """Validate environment variable names and values."""
    for key, value in env_vars.items():
        # Validate key format
        if not re.match(r'^[A-Z][A-Z0-9_]*$', key):
            raise ValidationError(f"Invalid environment variable name: {key}")
        
        # Check for sensitive data patterns
        sensitive_patterns = [
            r'password',
            r'secret',
            r'key',
            r'token',
        ]
        
        key_lower = key.lower()
        if any(pattern in key_lower for pattern in sensitive_patterns):
            if isinstance(value, str) and len(value) > 0:
                # Don't log sensitive values
                logger.warning("Sensitive environment variable detected", key=key)


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    # Remove or replace unsafe characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    
    # Ensure it's not empty
    if not sanitized:
        sanitized = "unnamed_file"
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        sanitized = name[:max_name_length] + ('.' + ext if ext else '')
    
    return sanitized


def validate_content_length(content: bytes, max_size: Optional[int] = None) -> None:
    """Validate content length."""
    max_size = max_size or settings.max_file_size
    
    if len(content) > max_size:
        raise ValidationError(
            f"Content too large. Maximum size is {max_size} bytes ({max_size // (1024*1024)} MB)"
        )
    
    if len(content) == 0:
        raise ValidationError("Content is empty")
