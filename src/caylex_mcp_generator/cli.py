"""
Command-line interface for the Caylex MCP Generator.
"""

import argparse
import asyncio
import sys
from pathlib import Path

import uvicorn

from .config import settings
from .main import app


def create_parser() -> argparse.ArgumentParser:
    """Create the command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Caylex MCP Generator - Generate FastMCP 2.0 servers from OpenAPI specifications"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Server command
    server_parser = subparsers.add_parser("serve", help="Start the MCP generator server")
    server_parser.add_argument(
        "--host",
        default=settings.host,
        help=f"Server host (default: {settings.host})"
    )
    server_parser.add_argument(
        "--port",
        type=int,
        default=settings.port,
        help=f"Server port (default: {settings.port})"
    )
    server_parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    server_parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error", "critical"],
        default=settings.log_level.lower(),
        help=f"Log level (default: {settings.log_level.lower()})"
    )
    
    # Version command
    version_parser = subparsers.add_parser("version", help="Show version information")
    
    # Health command
    health_parser = subparsers.add_parser("health", help="Check service health")
    health_parser.add_argument(
        "--url",
        default=f"http://{settings.host}:{settings.port}",
        help="Service URL to check"
    )
    
    return parser


async def check_health(url: str) -> bool:
    """Check the health of a running service."""
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{url}/health", timeout=5.0)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Service is healthy")
                print(f"   Status: {data.get('status')}")
                print(f"   Service: {data.get('service')}")
                print(f"   Version: {data.get('version')}")
                print(f"   Timestamp: {data.get('timestamp')}")
                return True
            else:
                print(f"❌ Service returned status code: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Failed to connect to service: {e}")
        return False


def serve_command(args) -> None:
    """Start the MCP generator server."""
    print(f"🚀 Starting Caylex MCP Generator server...")
    print(f"   Host: {args.host}")
    print(f"   Port: {args.port}")
    print(f"   Log Level: {args.log_level}")
    print(f"   Reload: {args.reload}")
    print()
    
    uvicorn.run(
        "caylex_mcp_generator.main:app",
        host=args.host,
        port=args.port,
        log_level=args.log_level,
        reload=args.reload,
    )


def version_command(args) -> None:
    """Show version information."""
    from . import __version__
    
    print(f"Caylex MCP Generator v{__version__}")
    print(f"FastMCP 2.0 compatible")
    print(f"Python {sys.version}")


async def health_command(args) -> None:
    """Check service health."""
    print(f"🔍 Checking health of service at {args.url}")
    
    healthy = await check_health(args.url)
    sys.exit(0 if healthy else 1)


def main() -> None:
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if args.command == "serve":
        serve_command(args)
    elif args.command == "version":
        version_command(args)
    elif args.command == "health":
        asyncio.run(health_command(args))
    else:
        # Default to serve command
        args.host = settings.host
        args.port = settings.port
        args.reload = settings.debug
        args.log_level = settings.log_level.lower()
        serve_command(args)


if __name__ == "__main__":
    main()
