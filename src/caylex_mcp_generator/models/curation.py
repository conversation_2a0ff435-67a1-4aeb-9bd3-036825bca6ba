"""
Tool curation models.
"""

from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class TemplateType(str, Enum):
    """Available template types for tool selection."""
    BASIC_CRUD = "basic_crud"
    FULL_API = "full_api"
    READ_ONLY = "read_only"
    ADMIN_TOOLS = "admin_tools"
    CORE_FEATURES = "core_features"
    CUSTOM = "custom"


class RiskLevel(str, Enum):
    """Risk levels for tool selection."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ToolCustomization(BaseModel):
    """Customization options for tool curation."""
    
    server_name: str = Field(description="Custom server name")
    include_auth: bool = Field(default=True, description="Whether to include authentication")
    risk_level: RiskLevel = Field(default=RiskLevel.MEDIUM, description="Acceptable risk level")
    max_tools: Optional[int] = Field(default=None, ge=1, description="Maximum number of tools to include")
    exclude_patterns: List[str] = Field(default_factory=list, description="Patterns to exclude from tool names/paths")
    include_patterns: List[str] = Field(default_factory=list, description="Patterns to include in tool names/paths")
    custom_descriptions: Dict[str, str] = Field(default_factory=dict, description="Custom descriptions for specific tools")


class CurationRequest(BaseModel):
    """Request for tool curation."""
    
    session_id: str = Field(description="Session identifier")
    selected_tools: List[str] = Field(description="List of selected tool operation IDs")
    template_type: TemplateType = Field(description="Template type for tool selection")
    customizations: ToolCustomization = Field(description="Customization preferences")


class CurationResponse(BaseModel):
    """Response for tool curation."""
    
    success: bool = Field(description="Whether curation was successful")
    message: Optional[str] = Field(default=None, description="Success or error message")
    curated_tool_count: Optional[int] = Field(default=None, description="Number of tools after curation")
    applied_filters: Optional[List[str]] = Field(default=None, description="List of filters that were applied")
