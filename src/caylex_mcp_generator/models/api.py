"""
API request/response models.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .analysis import AnalysisResult
from .generation import DeploymentFile


class HealthResponse(BaseModel):
    """Health check response."""
    
    status: str = Field(default="OK", description="Service status")
    service: str = Field(default="caylex-mcp-generator", description="Service name")
    version: str = Field(default="1.0.0", description="Service version")
    timestamp: str = Field(description="Current timestamp in ISO format")


class ErrorResponse(BaseModel):
    """Standard error response."""
    
    error: str = Field(description="Error type")
    message: str = Field(description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: str = Field(description="Error timestamp in ISO format")


class UploadResponse(BaseModel):
    """Response for file upload and analysis."""
    
    analysis_result: AnalysisResult = Field(description="Complete analysis results")


class IntegrationRequest(BaseModel):
    """Request for Caylex Directory integration."""
    
    session_id: str = Field(description="Session identifier")
    server_config: "ServerConfig" = Field(description="Server configuration for directory")


class ServerConfig(BaseModel):
    """Server configuration for directory integration."""
    
    server_name: str = Field(description="Server name for directory")
    deployment_url: str = Field(description="Server deployment URL")
    description: str = Field(description="Server description")
    generate_deployment_files: bool = Field(default=True, description="Whether to generate deployment files")
    tags: List[str] = Field(default_factory=list, description="Server tags for categorization")
    category: Optional[str] = Field(default=None, description="Server category")


class IntegrationResponse(BaseModel):
    """Response for directory integration."""
    
    success: bool = Field(description="Whether integration was successful")
    server_id: Optional[str] = Field(default=None, description="Generated server ID in directory")
    deployment_files: List[DeploymentFile] = Field(default_factory=list, description="Generated deployment files")
    message: str = Field(description="Success or error message")
    directory_url: Optional[str] = Field(default=None, description="URL to server in directory")


# Update forward reference
IntegrationRequest.model_rebuild()
