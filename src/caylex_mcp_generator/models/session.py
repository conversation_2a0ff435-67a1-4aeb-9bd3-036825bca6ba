"""
Session management models.
"""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class SessionStatus(str, Enum):
    """Session status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    COMPLETED = "completed"
    ERROR = "error"


class Session(BaseModel):
    """Session data model."""
    
    session_id: UUID = Field(default_factory=uuid4, description="Unique session identifier")
    status: SessionStatus = Field(default=SessionStatus.ACTIVE, description="Current session status")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Session creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    expires_at: Optional[datetime] = Field(default=None, description="Session expiration timestamp")
    
    # Workflow state tracking
    has_analysis: bool = Field(default=False, description="Whether OpenAPI analysis has been completed")
    has_curation: bool = Field(default=False, description="Whether tool curation has been applied")
    has_generation: bool = Field(default=False, description="Whether MCP server generation has been completed")
    
    # Data storage (in-memory for this implementation)
    analysis_data: Optional[dict] = Field(default=None, description="Stored analysis results")
    curation_data: Optional[dict] = Field(default=None, description="Stored curation preferences")
    generation_data: Optional[dict] = Field(default=None, description="Stored generation results")
    
    # Error tracking
    last_error: Optional[str] = Field(default=None, description="Last error message if any")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z",
            UUID: str,
        }
    
    def update_status(self, status: SessionStatus, error: Optional[str] = None) -> None:
        """Update session status and timestamp."""
        self.status = status
        self.updated_at = datetime.utcnow()
        if error:
            self.last_error = error
    
    def is_expired(self) -> bool:
        """Check if session has expired."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def mark_analysis_complete(self, analysis_data: dict) -> None:
        """Mark analysis as complete and store data."""
        self.has_analysis = True
        self.analysis_data = analysis_data
        self.updated_at = datetime.utcnow()
    
    def mark_curation_complete(self, curation_data: dict) -> None:
        """Mark curation as complete and store data."""
        self.has_curation = True
        self.curation_data = curation_data
        self.updated_at = datetime.utcnow()
    
    def mark_generation_complete(self, generation_data: dict) -> None:
        """Mark generation as complete and store data."""
        self.has_generation = True
        self.generation_data = generation_data
        self.updated_at = datetime.utcnow()


class SessionResponse(BaseModel):
    """Session information response."""
    
    session_id: str = Field(description="Session identifier")
    status: SessionStatus = Field(description="Current session status")
    created_at: str = Field(description="Session creation timestamp (ISO format)")
    has_analysis: bool = Field(description="Whether analysis has been completed")
    has_curation: bool = Field(description="Whether curation has been applied")
    has_generation: bool = Field(description="Whether generation has been completed")


class CreateSessionResponse(BaseModel):
    """Response for session creation."""
    
    session_id: str = Field(description="New session identifier")
    status: SessionStatus = Field(description="Initial session status")
