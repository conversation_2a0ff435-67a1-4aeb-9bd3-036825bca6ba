"""
Session management service.
"""

import asyncio
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional
from uuid import UUID

import structlog

from ..config import settings
from ..models.session import Session, SessionStatus

logger = structlog.get_logger(__name__)


class SessionManager:
    """Manages user sessions for the MCP generator workflow."""
    
    def __init__(self):
        self._sessions: Dict[str, Session] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def start(self) -> None:
        """Start the session manager and cleanup task."""
        logger.info("Starting session manager")
        self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop(self) -> None:
        """Stop the session manager and cleanup task."""
        logger.info("Stopping session manager")
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
    
    async def create_session(self) -> Session:
        """Create a new session."""
        async with self._lock:
            # Check session limit
            if len(self._sessions) >= settings.max_sessions:
                # Clean up expired sessions first
                await self._cleanup_expired_sessions_sync()
                
                # If still at limit, raise error
                if len(self._sessions) >= settings.max_sessions:
                    raise ValueError("Maximum number of sessions reached")
            
            # Create new session
            session = Session()
            session.expires_at = datetime.now(timezone.utc) + timedelta(seconds=settings.session_timeout)
            
            self._sessions[str(session.session_id)] = session
            
            logger.info(
                "Created new session",
                session_id=str(session.session_id),
                expires_at=session.expires_at.isoformat()
            )
            
            return session
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        async with self._lock:
            session = self._sessions.get(session_id)
            
            if session is None:
                logger.warning("Session not found", session_id=session_id)
                return None
            
            # Check if expired
            if session.is_expired():
                logger.info("Session expired", session_id=session_id)
                session.update_status(SessionStatus.EXPIRED)
                return session
            
            return session
    
    async def update_session(self, session_id: str, session: Session) -> None:
        """Update a session."""
        async with self._lock:
            if session_id in self._sessions:
                self._sessions[session_id] = session
                logger.debug("Updated session", session_id=session_id)
            else:
                logger.warning("Attempted to update non-existent session", session_id=session_id)
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session."""
        async with self._lock:
            if session_id in self._sessions:
                del self._sessions[session_id]
                logger.info("Deleted session", session_id=session_id)
                return True
            else:
                logger.warning("Attempted to delete non-existent session", session_id=session_id)
                return False
    
    async def get_session_count(self) -> int:
        """Get the current number of active sessions."""
        async with self._lock:
            return len(self._sessions)
    
    async def _cleanup_expired_sessions(self) -> None:
        """Background task to clean up expired sessions."""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                await self._cleanup_expired_sessions_sync()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in session cleanup task", error=str(e))
    
    async def _cleanup_expired_sessions_sync(self) -> None:
        """Clean up expired sessions (must be called with lock held)."""
        now = datetime.now(timezone.utc)
        expired_sessions = []
        
        for session_id, session in self._sessions.items():
            if session.expires_at and now > session.expires_at:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self._sessions[session_id]
            logger.info("Cleaned up expired session", session_id=session_id)
        
        if expired_sessions:
            logger.info("Cleaned up expired sessions", count=len(expired_sessions))


# Global session manager instance
session_manager = SessionManager()
