"""
MCP server code generation service.
"""

import os
import tempfile
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List

import structlog
from jinja2 import Environment, FileSystemLoader, Template

from ..config import settings
from ..models.analysis import OpenAPIEndpoint
from ..models.generation import (
    FileType,
    GeneratedFile,
    GenerationConfig,
    GenerationMetadata,
    TransportType,
)

logger = structlog.get_logger(__name__)


class CodeGenerator:
    """Generates FastMCP 2.0 server code from curated OpenAPI tools."""
    
    def __init__(self):
        # Setup Jinja2 environment
        template_dir = Path(__file__).parent.parent.parent.parent / "templates"
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            trim_blocks=True,
            lstrip_blocks=True,
        )
        
        # Add custom filters
        self.jinja_env.filters['tojsonfilter'] = self._to_json_filter

    def _to_json_filter(self, value):
        """Custom Jinja2 filter to convert value to JSON string."""
        import json
        if isinstance(value, str):
            return json.dumps(value)
        elif isinstance(value, (list, dict)):
            return json.dumps(value)
        else:
            return json.dumps(str(value))
    
    async def generate_mcp_server(
        self, 
        endpoints: List[OpenAPIEndpoint], 
        servers: List[str],
        config: GenerationConfig
    ) -> Dict:
        """Generate a complete MCP server from curated endpoints."""
        logger.info(
            "Starting MCP server generation",
            server_name=config.server_name,
            endpoint_count=len(endpoints),
            transport=config.transport
        )
        
        # Prepare template context
        context = {
            "config": config,
            "endpoints": endpoints,
            "servers": servers,
            "generation_timestamp": datetime.utcnow().isoformat() + "Z",
        }
        
        # Generate files
        generated_files = []
        
        # Main server file
        if config.typescript:
            server_file = await self._generate_typescript_server(context)
            generated_files.append(server_file)
            
            # TypeScript configuration
            tsconfig_file = await self._generate_tsconfig(context)
            generated_files.append(tsconfig_file)
        else:
            server_file = await self._generate_javascript_server(context)
            generated_files.append(server_file)
        
        # Package.json
        package_file = await self._generate_package_json(context)
        generated_files.append(package_file)
        
        # README.md
        readme_file = await self._generate_readme(context)
        generated_files.append(readme_file)
        
        # Environment file template
        env_file = await self._generate_env_template(context)
        generated_files.append(env_file)
        
        # Docker files (optional)
        if config.include_auth or config.transport == TransportType.STREAMABLE_HTTP:
            dockerfile = await self._generate_dockerfile(context)
            generated_files.append(dockerfile)
            
            docker_compose = await self._generate_docker_compose(context)
            generated_files.append(docker_compose)
        
        # Generate metadata
        metadata = GenerationMetadata(
            generator="caylex-mcp-generator",
            tool_count=len(endpoints),
            transport=config.transport,
            next_steps=self._generate_next_steps(config),
            dependencies=self._get_dependencies(config),
            environment_variables=self._get_environment_variables(config),
        )
        
        # Create ZIP file
        zip_path = await self._create_zip_file(generated_files, config.server_name)
        
        result = {
            "generated_files": generated_files,
            "metadata": metadata,
            "zip_path": zip_path,
            "config": config,
        }
        
        logger.info(
            "Completed MCP server generation",
            server_name=config.server_name,
            file_count=len(generated_files),
            zip_path=zip_path
        )
        
        return result
    
    async def _generate_typescript_server(self, context: Dict) -> GeneratedFile:
        """Generate TypeScript server file."""
        template = self.jinja_env.get_template("fastmcp_server.ts.j2")
        content = template.render(**context)
        
        return GeneratedFile(
            name="index.ts",
            type=FileType.TYPESCRIPT,
            path="src/index.ts",
            content=content,
            size=len(content.encode('utf-8'))
        )
    
    async def _generate_javascript_server(self, context: Dict) -> GeneratedFile:
        """Generate JavaScript server file."""
        # Use the same template but adjust for JavaScript
        template = self.jinja_env.get_template("fastmcp_server.ts.j2")
        content = template.render(**context)
        
        # Convert TypeScript to JavaScript (basic conversion)
        content = content.replace("import {", "const {")
        content = content.replace("} from '", "} = require('")
        content = content.replace("export default", "module.exports =")
        
        return GeneratedFile(
            name="index.js",
            type=FileType.JAVASCRIPT,
            path="index.js",
            content=content,
            size=len(content.encode('utf-8'))
        )
    
    async def _generate_package_json(self, context: Dict) -> GeneratedFile:
        """Generate package.json file."""
        template = self.jinja_env.get_template("package.json.j2")
        content = template.render(**context)
        
        return GeneratedFile(
            name="package.json",
            type=FileType.JSON,
            path="package.json",
            content=content,
            size=len(content.encode('utf-8'))
        )
    
    async def _generate_readme(self, context: Dict) -> GeneratedFile:
        """Generate README.md file."""
        template = self.jinja_env.get_template("README.md.j2")
        content = template.render(**context)
        
        return GeneratedFile(
            name="README.md",
            type=FileType.MARKDOWN,
            path="README.md",
            content=content,
            size=len(content.encode('utf-8'))
        )
    
    async def _generate_tsconfig(self, context: Dict) -> GeneratedFile:
        """Generate TypeScript configuration."""
        tsconfig_content = """{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}"""
        
        return GeneratedFile(
            name="tsconfig.json",
            type=FileType.JSON,
            path="tsconfig.json",
            content=tsconfig_content,
            size=len(tsconfig_content.encode('utf-8'))
        )
    
    async def _generate_env_template(self, context: Dict) -> GeneratedFile:
        """Generate environment template file."""
        config = context["config"]
        
        env_lines = [
            "# API Configuration",
            "API_KEY=your_api_key_here",
            "",
        ]
        
        if config.include_auth:
            env_lines.extend([
                "# Authentication",
                "API_TOKEN=your_bearer_token_here",
                "",
            ])
        
        if config.transport == TransportType.STREAMABLE_HTTP:
            env_lines.extend([
                "# Server Configuration",
                "HOST=127.0.0.1",
                f"PORT={config.port}",
                "",
            ])
        
        if config.include_logging:
            env_lines.extend([
                "# Logging",
                "LOG_LEVEL=info",
                "",
            ])
        
        if config.include_cors:
            env_lines.extend([
                "# CORS",
                "CORS_ORIGIN=*",
                "",
            ])
        
        content = "\n".join(env_lines)
        
        return GeneratedFile(
            name=".env.example",
            type=FileType.YAML,
            path=".env.example",
            content=content,
            size=len(content.encode('utf-8'))
        )

    async def _generate_dockerfile(self, context: Dict) -> GeneratedFile:
        """Generate Dockerfile."""
        config = context["config"]

        dockerfile_content = f"""FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
{"COPY src/ ./src/" if config.typescript else "COPY *.js ./"}
{"COPY tsconfig.json ./" if config.typescript else ""}

# Build TypeScript if needed
{"RUN npm run build" if config.typescript else ""}

# Create non-root user
RUN addgroup -g 1001 -S nodejs && adduser -S mcp -u 1001

# Change ownership
RUN chown -R mcp:nodejs /app
USER mcp

# Expose port for HTTP transport
{"EXPOSE " + str(config.port) if config.transport == TransportType.STREAMABLE_HTTP else ""}

# Start the server
CMD ["npm", "start"]
"""

        return GeneratedFile(
            name="Dockerfile",
            type=FileType.DOCKERFILE,
            path="Dockerfile",
            content=dockerfile_content,
            size=len(dockerfile_content.encode('utf-8'))
        )

    async def _generate_docker_compose(self, context: Dict) -> GeneratedFile:
        """Generate docker-compose.yml."""
        config = context["config"]

        compose_content = f"""version: '3.8'

services:
  {config.server_name}:
    build: .
    container_name: {config.server_name}
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - API_KEY=${{API_KEY}}
      {"- API_TOKEN=${API_TOKEN}" if config.include_auth else ""}
      {"- LOG_LEVEL=${LOG_LEVEL:-info}" if config.include_logging else ""}
    {"ports:" if config.transport == TransportType.STREAMABLE_HTTP else ""}
    {"  - \"" + str(config.port) + ":" + str(config.port) + "\"" if config.transport == TransportType.STREAMABLE_HTTP else ""}
    volumes:
      - ./logs:/app/logs
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
"""

        return GeneratedFile(
            name="docker-compose.yml",
            type=FileType.YAML,
            path="docker-compose.yml",
            content=compose_content,
            size=len(compose_content.encode('utf-8'))
        )

    async def _create_zip_file(self, files: List[GeneratedFile], server_name: str) -> str:
        """Create a ZIP file containing all generated files."""
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, f"{server_name}.zip")

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files:
                # Create directory structure in ZIP
                arcname = f"{server_name}/{file.path}"
                zipf.writestr(arcname, file.content)

        logger.info("Created ZIP file", zip_path=zip_path, file_count=len(files))
        return zip_path

    def _generate_next_steps(self, config: GenerationConfig) -> List[str]:
        """Generate next steps for deployment."""
        steps = [
            "Extract the ZIP file to your desired location",
            "Install dependencies with 'npm install'",
        ]

        if config.typescript:
            steps.append("Build the project with 'npm run build'")

        steps.extend([
            "Configure environment variables in .env file",
            "Set your API key and other required credentials",
        ])

        if config.transport == TransportType.STREAMABLE_HTTP:
            steps.append(f"Start the server with 'npm start' (will run on port {config.port})")
        else:
            steps.append("Run the server with 'npm start'")

        steps.extend([
            "Test the server with your MCP client",
            "Deploy to your preferred hosting platform",
        ])

        return steps

    def _get_dependencies(self, config: GenerationConfig) -> List[str]:
        """Get list of required dependencies."""
        deps = ["fastmcp>=2.0.0", "axios>=1.6.0"]

        if config.typescript:
            deps.extend(["typescript>=5.0.0", "@types/node>=20.0.0"])

        return deps

    def _get_environment_variables(self, config: GenerationConfig) -> Dict[str, str]:
        """Get required environment variables."""
        env_vars = {
            "API_KEY": "Your API key for authentication",
        }

        if config.include_auth:
            env_vars["API_TOKEN"] = "Bearer token for MCP authentication"

        if config.transport == TransportType.STREAMABLE_HTTP:
            env_vars["HOST"] = "Server host (default: 127.0.0.1)"
            env_vars["PORT"] = f"Server port (default: {config.port})"

        if config.include_logging:
            env_vars["LOG_LEVEL"] = "Logging level (default: info)"

        if config.include_cors:
            env_vars["CORS_ORIGIN"] = "CORS origin (default: *)"

        return env_vars
