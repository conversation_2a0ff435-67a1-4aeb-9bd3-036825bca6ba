# {{ config.name }}

{{ config.description }}

Generated by [Caylex MCP Generator](https://github.com/caylex/mcp-generator) using FastMCP 2.0.

## Features

- **{{ endpoints | length }} API Tools**: Generated from OpenAPI specification
- **FastMCP 2.0**: Built with the latest FastMCP framework
- **{{ config.transport | title }} Transport**: Uses {{ config.transport }} for communication
{% if config.include_auth %}
- **Authentication**: Bearer token authentication support
{% endif %}
{% if config.include_logging %}
- **Logging**: Structured logging with configurable levels
{% endif %}
{% if config.include_cors %}
- **CORS**: Cross-origin resource sharing support
{% endif %}

## Available Tools

{% for endpoint in endpoints %}
### {{ endpoint.operation_id }}

**{{ endpoint.method }} {{ endpoint.path }}**

{{ endpoint.summary }}

{% if endpoint.description %}
{{ endpoint.description }}
{% endif %}

**Parameters:**
{% for param in endpoint.parameters %}
- `{{ param.name }}` ({{ param.type }}){% if param.required %} *required*{% endif %}: {{ param.description }}
{% endfor %}

---

{% endfor %}

## Installation

```bash
# Install dependencies
npm install

{% if config.typescript %}
# Build the project
npm run build
{% endif %}
```

## Configuration

Set the following environment variables:

```bash
# API Configuration
API_KEY=your_api_key_here
{% if config.include_auth %}
API_TOKEN=your_bearer_token_here
{% endif %}

# Server Configuration
{% if config.transport == 'streamable-http' %}
HOST=127.0.0.1
PORT={{ config.port }}
{% endif %}
{% if config.include_logging %}
LOG_LEVEL=info
{% endif %}
{% if config.include_cors %}
CORS_ORIGIN=*
{% endif %}
```

## Usage

### Running the Server

```bash
# Production
npm start

# Development
npm run dev
```

### Using with MCP Clients

#### Claude Desktop

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "{{ config.server_name }}": {
      {% if config.transport == 'streamable-http' %}
      "command": "node",
      "args": ["{{ config.server_name }}/{% if config.typescript %}dist/{% endif %}index.js"],
      "env": {
        "API_KEY": "your_api_key_here"
      }
      {% else %}
      "command": "node",
      "args": ["{{ config.server_name }}/{% if config.typescript %}dist/{% endif %}index.js"]
      {% endif %}
    }
  }
}
```

#### FastMCP Client

```{% if config.typescript %}typescript{% else %}javascript{% endif %}
import { Client } from 'fastmcp';

const client = new Client('{{ config.server_name }}/{% if config.typescript %}dist/{% endif %}index.js');

async function example() {
  await client.connect();
  
  // List available tools
  const tools = await client.listTools();
  console.log('Available tools:', tools);
  
  // Call a tool
  const result = await client.callTool('{{ endpoints[0].operation_id }}', {
    // Add required parameters here
  });
  console.log('Result:', result);
  
  await client.disconnect();
}

example().catch(console.error);
```

## API Reference

This server provides access to the following API endpoints:

**Base URL**: {{ servers[0] if servers else "https://api.example.com" }}

{% for endpoint in endpoints %}
- **{{ endpoint.method }} {{ endpoint.path }}** - {{ endpoint.summary }}
{% endfor %}

## Development

{% if config.typescript %}
### TypeScript

This project is written in TypeScript. The source files are in `src/` and compiled to `dist/`.

```bash
# Watch for changes
npm run watch

# Build once
npm run build
```
{% endif %}

### Adding New Tools

To add new tools, modify the server code and add new `mcp.tool()` definitions following the existing patterns.

### Error Handling

All tools include comprehensive error handling and return structured responses with success/error status.

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:

1. Check the [FastMCP documentation](https://gofastmcp.com)
2. Review the generated code and configuration
3. Contact your API provider for API-specific issues

---

*Generated on {{ generation_timestamp }} by Caylex MCP Generator*
