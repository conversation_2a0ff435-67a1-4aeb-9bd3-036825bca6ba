{
  "name": "{{ config.server_name }}",
  "version": "{{ config.server_version }}",
  "description": "{{ config.description }}",
  "main": "{% if config.typescript %}dist/index.js{% else %}index.js{% endif %}",
  "scripts": {
    {% if config.typescript %}
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "ts-node src/index.ts",
    "watch": "tsc --watch",
    {% else %}
    "start": "node index.js",
    "dev": "node index.js",
    {% endif %}
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": [
    "mcp",
    "fastmcp",
    "model-context-protocol",
    "api",
    "openapi"
  ],
  "author": "Generated by Caylex MCP Generator",
  "license": "MIT",
  "dependencies": {
    "fastmcp": "^2.0.0",
    "axios": "^1.6.0"
  },
  {% if config.typescript %}
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "ts-node": "^10.9.0"
  },
  {% endif %}
  "engines": {
    "node": ">=18.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/your-org/{{ config.server_name }}.git"
  },
  "bugs": {
    "url": "https://github.com/your-org/{{ config.server_name }}/issues"
  },
  "homepage": "https://github.com/your-org/{{ config.server_name }}#readme"
}
