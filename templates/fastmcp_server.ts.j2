/**
 * {{ config.name }}
 * {{ config.description }}
 * 
 * Generated by Caylex MCP Generator
 * FastMCP 2.0 Server
 */

import { FastMCP } from 'fastmcp';
{% if config.include_auth %}
import { Bearer<PERSON><PERSON>Provider } from 'fastmcp/auth';
{% endif %}
{% if config.include_logging %}
import { LoggingMiddleware } from 'fastmcp/middleware';
{% endif %}
{% if config.include_cors %}
import { CorsMiddleware } from 'fastmcp/middleware';
{% endif %}
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

// Server configuration
const SERVER_CONFIG = {
  name: '{{ config.server_name }}',
  version: '{{ config.server_version }}',
  description: '{{ config.description }}',
  transport: '{{ config.transport }}',
  {% if config.transport == 'streamable-http' %}
  port: {{ config.port }},
  {% endif %}
};

// API configuration
const API_CONFIG = {
  baseURL: '{{ servers[0] if servers else "https://api.example.com" }}',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': `${SERVER_CONFIG.name}/${SERVER_CONFIG.version}`,
  },
};

// Create FastMCP server
const mcp = new FastMCP(SERVER_CONFIG.name, {
  description: SERVER_CONFIG.description,
  version: SERVER_CONFIG.version,
});

{% if config.include_auth %}
// Add authentication middleware
mcp.use(new BearerAuthProvider({
  validateToken: async (token: string) => {
    // Implement your token validation logic here
    return token === process.env.API_TOKEN;
  },
}));
{% endif %}

{% if config.include_logging %}
// Add logging middleware
mcp.use(new LoggingMiddleware({
  level: process.env.LOG_LEVEL || 'info',
}));
{% endif %}

{% if config.include_cors %}
// Add CORS middleware
mcp.use(new CorsMiddleware({
  origin: process.env.CORS_ORIGIN || '*',
}));
{% endif %}

// Create HTTP client
const httpClient: AxiosInstance = axios.create(API_CONFIG);

// Add request interceptor for authentication
httpClient.interceptors.request.use((config: AxiosRequestConfig) => {
  // Add authentication headers if needed
  const apiKey = process.env.API_KEY;
  if (apiKey) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${apiKey}`,
    };
  }
  return config;
});

// Add response interceptor for error handling
httpClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API request failed:', error.message);
    throw new Error(`API request failed: ${error.response?.status} ${error.response?.statusText}`);
  }
);

{% for endpoint in endpoints %}
/**
 * {{ endpoint.summary }}
 * {{ endpoint.description if endpoint.description else '' }}
 */
mcp.tool('{{ endpoint.operation_id }}', {
  description: '{{ endpoint.summary }}',
  parameters: {
    type: 'object',
    properties: {
      {% for param in endpoint.parameters %}
      {% if param.name != 'requestBody' %}
      {{ param.name }}: {
        type: '{{ param.type }}',
        description: '{{ param.description }}',
        {% if param.required %}required: true,{% endif %}
      },
      {% endif %}
      {% endfor %}
      {% if endpoint.parameters | selectattr('name', 'equalto', 'requestBody') | list %}
      requestBody: {
        type: 'object',
        description: 'Request body data',
      },
      {% endif %}
    },
    {% set required_params = endpoint.parameters | selectattr('required') | selectattr('name', 'ne', 'requestBody') | map(attribute='name') | list %}
    {% if required_params %}
    required: [{% for param in required_params %}"{{ param }}"{% if not loop.last %}, {% endif %}{% endfor %}],
    {% endif %}
  },
}, async (params: any) => {
  try {
    const config: AxiosRequestConfig = {
      method: '{{ endpoint.method.lower() }}',
      url: '{{ endpoint.path }}',
    };

    // Handle path parameters
    let url = config.url;
    {% for param in endpoint.parameters %}
    {% if param.in == 'path' %}
    if (params.{{ param.name }}) {
      url = url.replace('{{ "{" + param.name + "}" }}', String(params.{{ param.name }}));
    }
    {% endif %}
    {% endfor %}
    config.url = url;

    // Handle query parameters
    const queryParams: Record<string, any> = {};
    {% for param in endpoint.parameters %}
    {% if param.in == 'query' %}
    if (params.{{ param.name }} !== undefined) {
      queryParams.{{ param.name }} = params.{{ param.name }};
    }
    {% endif %}
    {% endfor %}
    if (Object.keys(queryParams).length > 0) {
      config.params = queryParams;
    }

    // Handle headers
    const headers: Record<string, string> = {};
    {% for param in endpoint.parameters %}
    {% if param.in == 'header' %}
    if (params.{{ param.name }}) {
      headers['{{ param.name }}'] = String(params.{{ param.name }});
    }
    {% endif %}
    {% endfor %}
    if (Object.keys(headers).length > 0) {
      config.headers = { ...config.headers, ...headers };
    }

    // Handle request body
    {% if endpoint.parameters | selectattr('name', 'equalto', 'requestBody') | list %}
    if (params.requestBody) {
      config.data = params.requestBody;
    }
    {% endif %}

    const response = await httpClient.request(config);
    
    return {
      success: true,
      status: response.status,
      data: response.data,
      headers: response.headers,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
    };
  }
});

{% endfor %}

// Health check tool
mcp.tool('health_check', {
  description: 'Check the health status of the MCP server',
  parameters: {
    type: 'object',
    properties: {},
  },
}, async () => {
  return {
    status: 'healthy',
    server: SERVER_CONFIG.name,
    version: SERVER_CONFIG.version,
    timestamp: new Date().toISOString(),
    tools_count: {{ endpoints | length }},
  };
});

// Start the server
if (require.main === module) {
  {% if config.transport == 'streamable-http' %}
  mcp.run({
    transport: 'http',
    host: process.env.HOST || '127.0.0.1',
    port: parseInt(process.env.PORT || '{{ config.port }}'),
  });
  {% else %}
  mcp.run();
  {% endif %}
}

export default mcp;
